<template>
  <div v-loading="loading" class="flex h-full flex-col px-10 pt-5">
    <h2 class="mt-[25px] cursor-pointer text-[28px] font-bold" @click="emit('gotoDataset')">
      <el-icon color="#939899" size="20px">
        <ArrowLeft />
      </el-icon>
      {{ tableName }}
    </h2>

    <el-table :data="subData" style="width: 100%" class="c-table-header mt-4 mb-4 h-0 flex-1">
      <el-table-column prop="id" label="字段ID" width="100">
        <template #default="{ row }">
          <el-button link type="primary" @click="gotoFiled(row)">
            {{ row.id }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tableName" label="量表英文名称" />
      <el-table-column prop="tableChineseName" label="量表中文名称">
        <template #default="{ row }">
          {{ row.tableChineseName }}<span v-if="row.visitPhaseChinese">（{{ row.visitPhaseChinese }}）</span>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="变量名称" />
      <el-table-column prop="chineseMeaning" label="变量中文含义" />
      <el-table-column label="类型">
        <template #default="{ row }">
          <span>{{ fieldTypeText(row.valueType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="{ row }">
          <el-button link type="primary" @click="gotoFiled(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="flex justify-center pb-5">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :page-size="pagination.pageSize"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { findMedicalFieldsByFileInforIdAndDynamicConditions } from '@/api/index';
  import { fieldTypeText } from '@/utils/format';
  import { ElTable } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  interface Props {
    tableId: number;
    tableName: string;
  }
  const props = defineProps<Props>();
  const emit = defineEmits<{ gotoDataset: []; gotoFiled: [id: number] }>();
  const loading = ref(true);
  const subData = ref<MedicalFieldVO[]>([]);

  const total = ref(0);
  const pagination = reactive({
    page: 1,
    pageSize: 15,
  });
  // 事件处理函数
  const handleCurrentChange = (e: number) => {
    pagination.page = e;
    fetchData();
  };

  const gotoFiled = (row: MedicalFieldVO) => {
    emit('gotoFiled', row.id!);
  };

  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findMedicalFieldsByFileInforIdAndDynamicConditions(+props.tableId, {
        pageNum: pagination.page,
        pageSize: pagination.pageSize,
      });
      subData.value = data?.content || [];
      total.value = data?.totalElement || 0;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  watchEffect(() => {
    if (props.tableId) {
      fetchData();
    }
  });
</script>

<style lang="scss" scoped>
  .c-table-header {
    :deep(.el-checkbox__inner) {
      border-color: #007f99;
    }
  }
</style>
